/* Remove all action buttons from product cards across all platforms */
.product-loop__buttons,
.quick-view-button,
.wishlist-button,
.compare-button,
.product-loop_button,
.product-loop_action,
.product-inner .product-loop__buttons,
.product-inner .quick-view-button,
.product-inner .wishlist-button,
.product-inner .compare-button,
.product-inner .product-loop_button,
.product-inner .product-loop_action,
.featured-products .product-loop__buttons,
.featured-products .quick-view-button,
.featured-products .wishlist-button,
.featured-products .compare-button,
.essential-products .product-loop__buttons,
.essential-products .quick-view-button,
.essential-products .wishlist-button,
.essential-products .compare-button,
.widget-products-with-category .product-loop__buttons,
.widget-products-with-category .quick-view-button,
.widget-products-with-category .wishlist-button,
.widget-products-with-category .compare-button,
.slick-slide .product-loop__buttons,
.slick-slide .quick-view-button,
.slick-slide .wishlist-button,
.slick-slide .compare-button,
[data-url*="quick-view"],
[data-url*="wishlist"],
[data-url*="compare"],
[href="#svg-icon-quick-view"],
[href="#svg-icon-wishlist"],
[href="#svg-icon-compare"],
.bb-btn-compare,
.bb-btn-wishlist,
.desktop-only-buttons-container {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
    width: 0 !important;
    height: 0 !important;
    position: absolute !important;
    z-index: -9999 !important;
    clip: rect(0, 0, 0, 0) !important;
}
