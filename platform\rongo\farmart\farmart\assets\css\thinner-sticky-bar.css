/* Thinner sticky bar for mobile and tablet */
@media (max-width: 991px) {
    /* Make the sticky bar thinner */
    #sticky-add-to-cart .sticky-atc-wrap {
        padding: 5px 0 !important;
    }
    
    /* Reduce the height of the quantity selector */
    #sticky-add-to-cart .quantity {
        margin-bottom: 5px !important;
    }
    
    #sticky-add-to-cart .quantity .qty-box {
        height: 28px !important;
    }
    
    #sticky-add-to-cart .quantity .svg-icon {
        width: 24px !important;
        height: 24px !important;
    }
    
    #sticky-add-to-cart .quantity .svg-icon svg {
        width: 12px !important;
        height: 12px !important;
    }
    
    #sticky-add-to-cart .quantity .qty {
        height: 24px !important;
        font-size: 14px !important;
    }
    
    /* Make the buttons smaller */
    #sticky-add-to-cart .btn {
        padding: 6px 5px !important;
        font-size: 14px !important;
    }
    
    #sticky-add-to-cart .btn .svg-icon {
        width: 16px !important;
        height: 16px !important;
    }
    
    #sticky-add-to-cart .btn .svg-icon svg {
        width: 14px !important;
        height: 14px !important;
    }
    
    /* Adjust the bottom padding of the page */
    .single-product .product-detail-container {
        padding-bottom: 100px !important;
    }
    
    /* Make the label smaller */
    #sticky-add-to-cart .quantity .label-quantity {
        font-size: 14px !important;
    }
    
    /* Compact layout for the sticky bar */
    #sticky-add-to-cart .sticky-atc-btn {
        padding: 0 10px !important;
    }
}
