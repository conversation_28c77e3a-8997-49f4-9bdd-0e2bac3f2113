// Fix for price tag height in mobile view
document.addEventListener('DOMContentLoaded', function() {
    if (window.innerWidth <= 991) {
        // Fix for related products and more to love sections
        function fixPriceTagHeight() {
            // Target all price tag containers in related products and more to love sections
            const priceTagContainers = document.querySelectorAll('.related-products-grid .product-inner .product-details > div, .more-to-love-products-grid .product-inner .product-details > div');
            
            priceTagContainers.forEach(function(container) {
                // Ensure all price tag containers have the same height
                container.style.height = '40px';
                container.style.minHeight = '40px';
                
                // Fix for sale price containers (with both sale and regular price)
                const saleContainer = container.querySelector('div[style*="flex-direction: column"]');
                if (saleContainer) {
                    saleContainer.style.height = '40px';
                    saleContainer.parentElement.style.height = '40px';
                }
            });
        }
        
        // Run the fix on page load
        fixPriceTagHeight();
        
        // Run the fix again after a short delay to ensure all elements are loaded
        setTimeout(fixPriceTagHeight, 500);
        
        // Run the fix when window is resized
        window.addEventListener('resize', fixPriceTagHeight);
        
        // Run the fix when new content is loaded (for pagination or "load more" functionality)
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes.length > 0) {
                    fixPriceTagHeight();
                }
            });
        });
        
        // Observe changes to the related products and more to love containers
        const relatedProductsContainer = document.querySelector('.related-products-grid');
        const moreToLoveContainer = document.querySelector('.more-to-love-products-grid');
        
        if (relatedProductsContainer) {
            observer.observe(relatedProductsContainer, { childList: true, subtree: true });
        }
        
        if (moreToLoveContainer) {
            observer.observe(moreToLoveContainer, { childList: true, subtree: true });
        }
    }
});
