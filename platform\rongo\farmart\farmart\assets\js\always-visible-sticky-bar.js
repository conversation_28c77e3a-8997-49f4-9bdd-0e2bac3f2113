// Script to ensure the sticky bar is always visible on mobile and tablet
(function() {
    'use strict';
    
    // Function to handle the sticky bar visibility
    function handleStickyBar() {
        if (window.innerWidth < 992) {
            var stickyBar = document.querySelector('#sticky-add-to-cart .sticky-atc-wrap');
            if (stickyBar) {
                // Force the sticky bar to be visible
                stickyBar.style.display = 'block';
                stickyBar.style.visibility = 'visible';
                stickyBar.style.opacity = '1';
                stickyBar.style.position = 'fixed';
                stickyBar.style.bottom = '0';
                stickyBar.style.left = '0';
                stickyBar.style.right = '0';
                stickyBar.style.width = '100%';
                stickyBar.style.zIndex = '9999';
                stickyBar.style.transform = 'none';
                stickyBar.style.backgroundColor = '#fff';
                stickyBar.style.boxShadow = '0 -2px 10px rgba(0, 0, 0, 0.1)';
                stickyBar.style.padding = '10px 0';
            }
        }
    }
    
    // Run on page load
    document.addEventListener('DOMContentLoaded', function() {
        handleStickyBar();
        
        // Run on scroll and resize
        window.addEventListener('scroll', handleStickyBar);
        window.addEventListener('resize', handleStickyBar);
        
        // Run periodically to ensure it stays visible
        setInterval(handleStickyBar, 500);
        
        // Handle quantity buttons
        var decreaseBtn = document.querySelector('#sticky-add-to-cart .decrease');
        var increaseBtn = document.querySelector('#sticky-add-to-cart .increase');
        var qtyInput = document.querySelector('#sticky-add-to-cart .qty');
        
        if (decreaseBtn && qtyInput) {
            decreaseBtn.addEventListener('click', function() {
                var currentVal = parseInt(qtyInput.value);
                var min = parseInt(qtyInput.getAttribute('min')) || 1;
                if (currentVal > min) {
                    qtyInput.value = currentVal - 1;
                }
            });
        }
        
        if (increaseBtn && qtyInput) {
            increaseBtn.addEventListener('click', function() {
                var currentVal = parseInt(qtyInput.value);
                var max = parseInt(qtyInput.getAttribute('max')) || 1000;
                if (currentVal < max) {
                    qtyInput.value = currentVal + 1;
                }
            });
        }
    });
})();
