<?php

namespace Theme\Farmart\Http\Controllers;

use Botble\Base\Http\Responses\BaseHttpResponse;
use <PERSON><PERSON>ble\Theme\Facades\Theme;
use Bo<PERSON>ble\Theme\Http\Controllers\PublicController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Theme\Farmart\Supports\Wishlist;

class AjaxController extends PublicController
{
    /**
     * Get more products for the "More to Love" section
     *
     * @param Request $request
     * @param BaseHttpResponse $response
     * @return BaseHttpResponse
     */
    public function getMoreToLoveProducts(Request $request, BaseHttpResponse $response)
    {
        $page = (int)$request->input('page', 1);
        $perPage = 30;

        // Get the current product ID from the request (if available)
        $currentProductId = (int)$request->input('product_id', 0);

        // Get already shown product IDs to prevent duplicates in current session
        $shownProductIds = $request->input('shown_ids', []);
        if (is_string($shownProductIds)) {
            $shownProductIds = explode(',', $shownProductIds);
        }
        $shownProductIds = array_filter(array_map('intval', $shownProductIds));

        // Base condition for all products
        $baseCondition = [
            'ec_products.status' => \Botble\Base\Enums\BaseStatusEnum::PUBLISHED,
            'ec_products.is_variation' => 0, // Exclude variation products
        ];

        // Exclude current product if ID is provided
        if ($currentProductId > 0) {
            $baseCondition[] = ['ec_products.id', '!=', $currentProductId];
        }

        // Get total count of all available products
        $totalAvailableProducts = get_products([
            'condition' => $baseCondition,
            'take' => null,
            'with' => [],
        ]);

        $totalProductsCount = $totalAvailableProducts->count();

        // Debug logging
        \Log::info('More to Love Debug - Request Start', [
            'total_products' => $totalProductsCount,
            'shown_products_count' => count($shownProductIds),
            'shown_product_ids' => $shownProductIds,
            'page' => $page,
            'raw_shown_ids_input' => $request->input('shown_ids', 'NOT_SET')
        ]);

        // Check if we need to reset the cycle
        $resetCycle = false;
        if (!empty($shownProductIds) && count($shownProductIds) >= $totalProductsCount) {
            $shownProductIds = []; // Reset to show all products again
            $resetCycle = true;
            \Log::info('Cycle reset triggered');
        }

        // Build final condition
        $finalCondition = $baseCondition;
        if (!empty($shownProductIds)) {
            $finalCondition[] = ['ec_products.id', 'NOT IN', $shownProductIds];
        }

        // Get products and shuffle them
        $allAvailableProducts = get_products([
            'condition' => $finalCondition,
            'take' => null, // Get all available products
            'with' => [
                'slugable',
                'variations',
                'productLabels',
                'variationAttributeSwatchesForProductList',
                'productCollections',
            ],
            'order_by' => ['ec_products.created_at' => 'DESC'],
        ]);

        \Log::info('Products found after filtering', [
            'available_products_count' => $allAvailableProducts->count(),
            'final_condition' => $finalCondition
        ]);

        // If no products available after filtering, reset the cycle
        if ($allAvailableProducts->isEmpty() && !$resetCycle) {
            // All products have been shown, reset and get fresh products
            $resetCycle = true;
            $shownProductIds = []; // Clear server-side tracking

            // Get products again without exclusions
            $allAvailableProducts = get_products([
                'condition' => $baseCondition,
                'take' => null,
                'with' => [
                    'slugable',
                    'variations',
                    'productLabels',
                    'variationAttributeSwatchesForProductList',
                    'productCollections',
                ],
                'order_by' => ['ec_products.created_at' => 'DESC'],
            ]);

            \Log::info('Forced cycle reset - no products available after filtering', [
                'products_after_reset' => $allAvailableProducts->count()
            ]);
        }

        // Shuffle and take the required amount
        $products = $allAvailableProducts->shuffle()->take($perPage);

        // For infinite cycling, we always have more products available
        // The only time we don't have more is if there are no products at all in the database
        $totalProductsCount = $totalAvailableProducts->count();
        $hasMore = $totalProductsCount > 0; // Always true if we have any products

        // If still no products found after reset, there are truly no products
        if ($products->isEmpty()) {
            return $response->setData([
                'data' => [
                    'products' => [],
                    'has_more' => false,
                    'message' => __('No products found'),
                ]
            ]);
        }

        $wishlistIds = Wishlist::getWishlistIds($products->pluck('id')->all());

        $data = [];
        $newProductIds = [];
        foreach ($products as $product) {
            $data[] = '<div class="product-inner bg-white">' . Theme::partial('ecommerce.product-item-grid', compact('product', 'wishlistIds')) . '</div>';
            $newProductIds[] = $product->id;
        }

        // Final debug before returning
        \Log::info('More to Love Debug - Response', [
            'new_product_ids' => $newProductIds,
            'reset_cycle' => $resetCycle,
            'original_shown_ids' => $shownProductIds,
            'final_condition_used' => $finalCondition,
            'products_found_count' => count($newProductIds)
        ]);

        return $response->setData([
            'data' => [
                'products' => $data,
                'has_more' => $hasMore,
                'new_product_ids' => $newProductIds,
                'reset_cycle' => $resetCycle,
                'total_products' => $totalProductsCount,
                'shown_count' => count($shownProductIds) + count($newProductIds),
                'available_products_count' => $allAvailableProducts->count(),
                'original_shown_ids' => $request->input('shown_ids', []),
                'message' => $resetCycle ? __('Starting new cycle - showing all products again!') : null,
            ]
        ]);
    }
}
