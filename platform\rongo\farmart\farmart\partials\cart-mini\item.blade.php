<li class="mini-cart-item">
    <div class="product-image">
        <a
            class="img-fluid-eq"
            href="{{ $product->original_product->url }}"
        >
            <div class="img-fluid-eq__dummy"></div>
            <div class="img-fluid-eq__wrap">
                <img
                    class="lazyload"
                    data-src="{{ RvMedia::getImageUrl(Arr::get($cartItem->options, 'image', $product->original_product->image), 'thumb', false, RvMedia::getDefaultImage()) }}"
                    alt="{{ $product->original_product->name }}"
                >
            </div>
        </a>
    </div>
    <div class="product-content">
        <div class="product-name">
            <a href="{{ $product->original_product->url }}">{{ $product->original_product->name }}</a>
        </div>
        @if (is_plugin_active('marketplace') && $product->original_product->store->id)
            <div class="product-vendor">
                <a href="{{ $product->original_product->store->url }}">
                    {{ $product->original_product->store->name }}
                </a>
            </div>
        @endif
        <div class="quantity">
            <span class="price-amount amount">
                <bdi>{{ format_price($cartItem->price) }} @if ($product->front_sale_price != $product->price)
                        <small><del>{{ format_price($product->price) }}</del></small>
                    @endif
                </bdi>
            </span>
            <span class="qty-text">({{ __('x:quantity', ['quantity' => $cartItem->qty]) }})</span>
        </div>
        @if (!empty(Arr::get($cartItem->options, 'attributes', '')))
            <div class="product-attributes">
                <small>{{ Arr::get($cartItem->options, 'attributes', '') }}</small>
            </div>
        @endif
        @if (EcommerceHelper::isEnabledProductOptions() && !empty($cartItem->options['options']))
            <div class="product-options">
                {!! render_product_options_html($cartItem->options['options'], $product->front_sale_price_with_taxes) !!}
            </div>
        @endif

        @include(
            EcommerceHelper::viewPath('includes.cart-item-options-extras'),
            ['options' => $cartItem->options]
        )
    </div>
    <div class="remove-item">
        <a
            class="btn remove-cart-item"
            data-url="{{ route('public.cart.remove', $cartItem->rowId) }}"
            href="#"
            aria-label="{{ __('Remove this item') }}"
        >
            <span class="svg-icon">
                <svg>
                    <use
                        href="#svg-icon-trash"
                        xlink:href="#svg-icon-trash"
                    ></use>
                </svg>
            </span>
        </a>
    </div>
</li>
