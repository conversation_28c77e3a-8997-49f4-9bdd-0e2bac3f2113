/* Sticky Add to Cart for Mobile and Tablet */

/* Hide the regular quantity/add to cart/buy now section for mobile and tablet */
@media (max-width: 991px) {
    /* Target all possible instances of the quantity selector and buttons */
    .product-details-content .product-button,
    .cart-form .product-button,
    .cart-form .quantity,
    .cart-form button[name="add_to_cart"],
    .cart-form button[name="checkout"],
    form.cart-form .quantity,
    form.cart-form .product-button,
    .js-product-content .quantity,
    .js-product-content button[name="add_to_cart"],
    .js-product-content button[name="checkout"],
    .hide-on-mobile,
    .quantity:not(.sticky-atc-wrap .quantity) {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
        position: absolute !important;
        pointer-events: none !important;
    }
}

/* Make the sticky add to cart always visible for mobile and tablet */
@media (max-width: 991px) {
    .sticky-atc-wrap {
        bottom: 0 !important;
        transform: translate3d(0, 0, 0) !important;
        z-index: 999 !important;
        padding: 10px 0;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        position: fixed !important;
        left: 0;
        right: 0;
        width: 100%;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Ensure the sticky bar is always shown regardless of scroll position */
    body.single-product .sticky-atc-wrap {
        transform: none !important;
        display: block !important;
    }

    /* Add quantity selector to sticky bar */
    .sticky-atc-wrap .sticky-atc-btn {
        display: flex !important;
        flex-wrap: wrap !important;
        align-items: center !important;
        justify-content: space-between !important;
        padding: 0 15px !important;
        background-color: #fff !important;
        visibility: visible !important;
        opacity: 1 !important;
        height: auto !important;
        overflow: visible !important;
        position: relative !important;
        pointer-events: auto !important;
    }

    /* Ensure the quantity selector in the sticky bar is visible */
    .sticky-atc-wrap .quantity {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        height: auto !important;
        overflow: visible !important;
        margin-bottom: 10px !important;
        padding: 0 !important;
        position: relative !important;
        pointer-events: auto !important;
    }

    /* Hide the header--product section on mobile and tablet */
    .header--product {
        display: none !important;
    }

    .sticky-atc-wrap .sticky-atc-btn .quantity {
        flex: 0 0 100%;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }

    .sticky-atc-wrap .sticky-atc-btn .quantity .label-quantity {
        margin-right: 10px;
        font-weight: 500;
    }

    .sticky-atc-wrap .sticky-atc-btn .quantity .qty-box {
        display: flex;
        align-items: center;
        border: 1px solid #ddd;
        border-radius: 3px;
    }

    .sticky-atc-wrap .sticky-atc-btn .quantity .svg-icon {
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

    .sticky-atc-wrap .sticky-atc-btn .quantity .qty {
        width: 50px;
        text-align: center;
        border: none;
        height: 30px;
        padding: 0;
        -moz-appearance: textfield;
    }

    .sticky-atc-wrap .sticky-atc-btn .quantity .qty::-webkit-outer-spin-button,
    .sticky-atc-wrap .sticky-atc-btn .quantity .qty::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    .sticky-atc-wrap .sticky-atc-btn .btn {
        flex: 0 0 48% !important;
        margin: 0 !important;
        padding: 8px 5px !important;
        display: inline-block !important;
        visibility: visible !important;
        opacity: 1 !important;
        height: auto !important;
        overflow: visible !important;
        position: relative !important;
        pointer-events: auto !important;
    }

    /* Adjust spacing for footer mobile menu if present */
    .footer-mobile {
        z-index: 1000 !important;
    }

    .single-product .footer-mobile {
        display: none !important;
    }

    /* Ensure proper spacing at the bottom of the page to accommodate the sticky bar */
    .single-product .product-detail-container {
        padding-bottom: 120px;
    }
}
