// <PERSON>ript to specifically target and remove the Wishlist and Compare buttons at the top of the product page
(function() {
    // Function to remove elements by their exact text content
    function removeElementsByExactText(text) {
        // Get all elements in the document
        var allElements = document.querySelectorAll('*');
        
        // Loop through all elements
        for (var i = 0; i < allElements.length; i++) {
            var element = allElements[i];
            
            // Check if this element has only text nodes as children
            if (element.childNodes.length > 0 && element.childNodes[0].nodeType === 3) {
                // Get the text content of this element
                var elementText = element.textContent.trim();
                
                // If the text matches exactly, remove this element
                if (elementText === text) {
                    if (element.parentNode) {
                        element.parentNode.removeChild(element);
                    }
                }
            }
        }
    }
    
    // Function to remove elements by their href attribute
    function removeElementsByHref(hrefPart) {
        // Get all anchor elements
        var anchors = document.querySelectorAll('a[href*="' + hrefPart + '"]');
        
        // Loop through all matching anchors and remove them
        for (var i = 0; i < anchors.length; i++) {
            var anchor = anchors[i];
            if (anchor.parentNode) {
                anchor.parentNode.removeChild(anchor);
            }
        }
    }
    
    // Function to remove specific elements in the header
    function removeSpecificHeaderElements() {
        // Get the header info element
        var headerInfo = document.querySelector('.header-info');
        if (headerInfo) {
            // Get all links in the header info
            var links = headerInfo.querySelectorAll('a');
            
            // Loop through all links
            for (var i = 0; i < links.length; i++) {
                var link = links[i];
                var text = link.textContent.trim();
                
                // If the link text is Wishlist or Compare, remove it
                if (text === 'Wishlist' || text === 'Compare') {
                    // Try to remove the parent li if it exists
                    var parentLi = link.closest('li');
                    if (parentLi) {
                        parentLi.parentNode.removeChild(parentLi);
                    } else if (link.parentNode) {
                        // Otherwise just remove the link
                        link.parentNode.removeChild(link);
                    }
                }
            }
        }
    }
    
    // Function to run all removal operations
    function runRemoval() {
        // Remove elements by exact text
        removeElementsByExactText('Wishlist');
        removeElementsByExactText('Compare');
        
        // Remove elements by href
        removeElementsByHref('wishlist');
        removeElementsByHref('compare');
        
        // Remove specific elements in the header
        removeSpecificHeaderElements();
    }
    
    // Run immediately
    runRemoval();
    
    // Also run when DOM is loaded
    document.addEventListener('DOMContentLoaded', runRemoval);
    
    // Run periodically to catch any dynamically added elements
    setInterval(runRemoval, 100);
})();
