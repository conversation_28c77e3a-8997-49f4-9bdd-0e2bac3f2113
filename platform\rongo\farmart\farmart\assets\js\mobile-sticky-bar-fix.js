/**
 * Fix for sticky add to cart bar on mobile and tablet platforms
 */
(function() {
    'use strict';

    // Function to initialize the sticky bar on mobile and tablet only
    function initMobileStickyBar() {
        // Only apply on mobile and tablet (screen width < 992px)
        if (window.innerWidth < 992) {
            // Find the sticky bar elements
            const stickyBar = document.querySelector('#sticky-add-to-cart .sticky-atc-wrap');
            const mobileStickyBar = document.querySelector('.mobile-sticky-add-to-cart');
            
            // If sticky bar exists, make sure it's visible
            if (stickyBar) {
                stickyBar.style.display = 'block';
                stickyBar.style.visibility = 'visible';
                stickyBar.style.opacity = '1';
                stickyBar.style.position = 'fixed';
                stickyBar.style.bottom = '60px'; // Position above footer
                stickyBar.style.left = '0';
                stickyBar.style.right = '0';
                stickyBar.style.width = '100%';
                stickyBar.style.zIndex = '998';
                stickyBar.style.transform = 'none';
                stickyBar.classList.add('sticky-atc-shown');
            }
            
            // If mobile sticky bar exists, make sure it's visible
            if (mobileStickyBar) {
                mobileStickyBar.style.display = 'block';
                mobileStickyBar.style.visibility = 'visible';
                mobileStickyBar.style.opacity = '1';
            }
            
            // If neither exists and we're on a product page, create the mobile sticky bar
            if (!stickyBar && !mobileStickyBar) {
                const productForm = document.querySelector('.cart-form');
                if (productForm) {
                    createMobileStickyBar(productForm);
                }
            }
        }
    }
    
    // Function to create the mobile sticky bar if it doesn't exist
    function createMobileStickyBar(productForm) {
        // Get product data
        const productId = productForm.querySelector('input[name="id"]')?.value;
        const productQuantity = productForm.querySelector('.qty')?.value || 1;
        
        if (!productId) return;
        
        // Create mobile sticky bar
        const mobileStickyBar = document.createElement('div');
        mobileStickyBar.className = 'mobile-sticky-add-to-cart';
        mobileStickyBar.innerHTML = `
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="sticky-add-to-cart-wrapper">
                            <div class="quantity">
                                <div class="qty-group">
                                    <a href="#" class="qty-decrease">-</a>
                                    <input type="text" class="qty-input" value="${productQuantity}" min="1" max="100">
                                    <a href="#" class="qty-increase">+</a>
                                </div>
                            </div>
                            <div class="action-buttons">
                                <button class="btn-add-to-cart" data-id="${productId}">Add to cart</button>
                                <button class="btn-buy-now" data-id="${productId}">Buy Now</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(mobileStickyBar);
        
        // Add event listeners
        setupMobileStickyBarEvents(mobileStickyBar, productForm);
    }
    
    // Function to set up event listeners for the mobile sticky bar
    function setupMobileStickyBarEvents(mobileStickyBar, productForm) {
        const qtyInput = mobileStickyBar.querySelector('.qty-input');
        const qtyDecrease = mobileStickyBar.querySelector('.qty-decrease');
        const qtyIncrease = mobileStickyBar.querySelector('.qty-increase');
        const addToCartBtn = mobileStickyBar.querySelector('.btn-add-to-cart');
        const buyNowBtn = mobileStickyBar.querySelector('.btn-buy-now');
        
        // Quantity change
        if (qtyInput) {
            qtyInput.addEventListener('change', function() {
                const mainQty = productForm.querySelector('.qty');
                if (mainQty) {
                    mainQty.value = this.value;
                }
            });
        }
        
        // Decrease quantity
        if (qtyDecrease) {
            qtyDecrease.addEventListener('click', function(e) {
                e.preventDefault();
                const input = this.parentNode.querySelector('.qty-input');
                let value = parseInt(input.value);
                if (value > 1) {
                    value--;
                    input.value = value;
                    const mainQty = productForm.querySelector('.qty');
                    if (mainQty) {
                        mainQty.value = value;
                    }
                }
            });
        }
        
        // Increase quantity
        if (qtyIncrease) {
            qtyIncrease.addEventListener('click', function(e) {
                e.preventDefault();
                const input = this.parentNode.querySelector('.qty-input');
                let value = parseInt(input.value);
                value++;
                input.value = value;
                const mainQty = productForm.querySelector('.qty');
                if (mainQty) {
                    mainQty.value = value;
                }
            });
        }
        
        // Add to cart
        if (addToCartBtn) {
            addToCartBtn.addEventListener('click', function() {
                const mainAddToCartBtn = productForm.querySelector('.add-to-cart-button');
                if (mainAddToCartBtn) {
                    mainAddToCartBtn.click();
                } else {
                    productForm.submit();
                }
            });
        }
        
        // Buy now
        if (buyNowBtn) {
            buyNowBtn.addEventListener('click', function() {
                const buyNowInput = document.createElement('input');
                buyNowInput.type = 'hidden';
                buyNowInput.name = 'checkout';
                buyNowInput.value = '1';
                
                productForm.appendChild(buyNowInput);
                productForm.submit();
            });
        }
    }
    
    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        initMobileStickyBar();
        
        // Also initialize on window resize
        window.addEventListener('resize', function() {
            // Only reinitialize if we're on mobile/tablet
            if (window.innerWidth < 992) {
                initMobileStickyBar();
            }
        });
    });
    
    // Initialize immediately for mobile and tablet
    if (window.innerWidth < 992) {
        initMobileStickyBar();
    }
})();
