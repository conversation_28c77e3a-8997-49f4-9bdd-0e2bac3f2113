// Script to specifically remove the Wishlist and Compare buttons at the top of the product page
document.addEventListener('DOMContentLoaded', function() {
    // Function to find and hide elements by text content
    function hideElementsByText(text) {
        var allElements = document.querySelectorAll('a, button, span');
        allElements.forEach(function(element) {
            if (element.textContent.trim() === text) {
                element.style.display = 'none';
                
                // Also hide parent elements
                var parent = element.parentElement;
                if (parent) {
                    parent.style.display = 'none';
                    
                    // Try to hide grandparent if it's a list item
                    if (parent.parentElement && parent.parentElement.tagName === 'LI') {
                        parent.parentElement.style.display = 'none';
                    }
                }
            }
        });
    }
    
    // Function to find and hide elements by href content
    function hideElementsByHref(partialHref) {
        var allLinks = document.querySelectorAll('a[href*="' + partialHref + '"]');
        allLinks.forEach(function(link) {
            link.style.display = 'none';
            
            // Also hide parent elements
            var parent = link.parentElement;
            if (parent) {
                parent.style.display = 'none';
                
                // Try to hide grandparent if it's a list item
                if (parent.parentElement && parent.parentElement.tagName === 'LI') {
                    parent.parentElement.style.display = 'none';
                }
            }
        });
    }
    
    // Hide elements by text content
    hideElementsByText('Wishlist');
    hideElementsByText('Compare');
    
    // Hide elements by href content
    hideElementsByHref('wishlist');
    hideElementsByHref('compare');
    
    // Target the specific elements in the screenshot
    var headerLinks = document.querySelectorAll('.header-info a');
    headerLinks.forEach(function(link) {
        var text = link.textContent.trim();
        if (text === 'Wishlist' || text === 'Compare') {
            link.style.display = 'none';
            
            // Also hide parent elements
            var parent = link.parentElement;
            if (parent) {
                parent.style.display = 'none';
            }
        }
    });
    
    // Try to find the specific elements by their position in the DOM
    var headerInfo = document.querySelector('.header-info');
    if (headerInfo) {
        var links = headerInfo.querySelectorAll('a');
        links.forEach(function(link, index) {
            var text = link.textContent.trim();
            if (text === 'Wishlist' || text === 'Compare') {
                link.style.display = 'none';
                
                // Also hide parent elements
                var parent = link.parentElement;
                if (parent) {
                    parent.style.display = 'none';
                }
            }
        });
    }
    
    // Create a MutationObserver to handle dynamically added elements
    var observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        // Check if the added node is a Wishlist or Compare button
                        if (node.textContent.trim() === 'Wishlist' || node.textContent.trim() === 'Compare') {
                            node.style.display = 'none';
                        }
                        
                        // Check children of the added node
                        var links = node.querySelectorAll('a');
                        links.forEach(function(link) {
                            var text = link.textContent.trim();
                            if (text === 'Wishlist' || text === 'Compare') {
                                link.style.display = 'none';
                                
                                // Also hide parent elements
                                var parent = link.parentElement;
                                if (parent) {
                                    parent.style.display = 'none';
                                }
                            }
                        });
                    }
                });
            }
        });
    });
    
    // Start observing the document with the configured parameters
    observer.observe(document.body, { childList: true, subtree: true });
});
