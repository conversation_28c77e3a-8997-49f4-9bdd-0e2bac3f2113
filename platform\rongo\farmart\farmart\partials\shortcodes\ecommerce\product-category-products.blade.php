{!! Theme::partial('ecommerce.product-grid-section', [
    'title' => $shortcode->title ?: $category->name,
    'products' => $products,
    'wishlistIds' => $wishlistIds
]) !!}

<style>
    .centered-btn-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 30px;
        margin-top: -25px;
        margin-bottom: 40px;
        background: #F8F9FA;
        padding: 24px 0;
        border-radius: 8px;
    }
    .centered-btn-container .btn.btn-primary {
        background: transparent !important;
        border: 2px solid #000 !important;
         border-radius: 30px; 
        color: #000 !important;
        box-shadow: none !important;
        transition: background 0.2s, color 0.2s, border-color 0.2s;
        
    }
    .centered-btn-container .btn.btn-primary:hover {
        background: #ff6633 !important;
        color: #fff !important;
        border-color: #ff6633 !important;
    }
</style>
<div class="centered-btn-container">
    <a href="{{ $category->url }}" class="btn btn-primary">
        View More
    </a>
</div>
