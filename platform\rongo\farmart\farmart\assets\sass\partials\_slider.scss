.section-content {
    &.section-content__slider {
        transition: background .3s, border .3s, border-radius .3s, box-shadow .3s;
        padding: 40px 15px;
        background-color: #f3f3f3;

        .section-slides-wrapper {
            border-radius: 10px;
            overflow: hidden;
            position: relative;

            .slick-slides-carousel:not(.slick-initialized) {
                height: 34vw;
                max-height: 460px;
            }

            .slide-item {
                position: relative;
                display: block !important;

                .slide-item__image {
                    height: 34vw;
                    max-height: 460px;
                    background-color: #d6dee5;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }
                }
            }

            .arrows-wrapper {
                position: absolute;
                right: 20px;
                bottom: 12px;
                z-index: 2;
            }
        }
    }
}

@media (max-width: 767px) {
    .section-content {
        &.section-content__slider {
            padding: 0 !important;

            .section-slides-wrapper {
                .slide-item {
                    .slide-item__image {
                        height: 52vw;
                    }
                }
            }
        }
    }
}

.arrows-wrapper {
    display: flex;

    .slick-arrow {
        display: block;
        width: 26px;
        height: 26px;
        line-height: 24px;
        border: 1px solid #ddd;
        border-radius: 3px;
        background-color: #fff;
        outline: none;
        text-align: center;
        font-size: 12px;
        transition: ease .5s;
        cursor: pointer;
        position: relative;
        transform: none;

        &.slick-prev-arrow {
            margin-right: 5px;
            left: unset;
        }

        &.slick-next-arrow {
            margin-left: 5px;
            right: unset;
        }

        &.slick-disabled {
            background-color: #fff;
        }

        &:hover {
            background-color: $color-primary;
        }
    }
}

.arrows-top-right {
    position: relative;

    .arrows-wrapper {
        position: absolute;
        top: -40px;
        right: 0;
        left: auto;
    }
}

.section-banner-wrapper {
    border-radius: 10px;
    overflow: hidden;

    .banner-medium {
        position: relative;

        .banner-item__image {
            height: 34vw;
            max-height: 460px;
            background-color: #d6dee5;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
    }
}

.widget-product-categories {
    .product-categories-body {
        .slick-track {
            display: flex;

            .slick-slide {
                height: inherit;

                > div {
                    height: 100%;
                }
            }
        }

        .product-category-item {
            height: inherit;

            .category-item-body {
                background-color: #f7f7f6;
                border-radius: 10px;
                height: 100%;

                .category__name {
                    font-size: 17px;
                    font-weight: 700;
                }

                &:hover {
                    background-color: #fff;
                    box-shadow: 0 1.5rem 2rem 0 rgb(131 132 135 / 15%);
                    transition: .5s;
                }
            }
        }
    }
}

.slick-dots {
    position: absolute;
    display: none;
    width: 100%;
    padding: 0;
    margin: 0;
    list-style: none;
    text-align: center;

    li {
        list-style: none;
        position: relative;
        display: inline-block;
        margin: 0 7px;
        padding: 0;
        cursor: pointer;

        button {
            font-size: 0;
            line-height: 0;
            display: block;
            width: 8px;
            height: 8px;
            padding: 0;
            cursor: pointer;
            color: transparent;
            border: 0;
            outline: none;
            background: 0 0;

            &:before {
                position: absolute;
                top: 0;
                left: 0;
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background-color: #ddd;
                transition: 0.5s;
                content: '';
                text-align: center;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }

        }

        &.slick-active {
            button:before {
                background-color: $color-primary;
            }
        }
    }
}

.slick-arrow {
    font-size: 11px;
    position: absolute;
    top: 50%;
    display: block;
    transition: ease .5s;
    width: 34px;
    height: 34px;
    line-height: 31px;
    padding: 0;
    transform: translate(0, -50%);
    cursor: pointer;
    color: #222;
    border: 1px solid #ddd;
    border-radius: 3px;
    background-color: #fff;
    outline: none;
    z-index: 1;
    text-align: center;

    &.slick-disabled {
        background-color: #ddd;
        cursor: not-allowed;
    }

    &.slick-next-arrow {
        right: 10px;
    }
}

.slick-slides-carousel {
    .slick-track {
        margin-left: 0;
        margin-right: 0;
    }
}
