<!-- Cart Content -->
<div class="modern-cart-content">
    @php
        $cart = Cart::instance('cart');
        $products = $cart->products();
    @endphp

    @if ($cart->isNotEmpty() && $products && $products->isNotEmpty())
        @foreach ($cart->content() as $key => $cartItem)
            @php
                $product = $products->find($cartItem->id);
            @endphp
            @if ($product)
                <div class="modern-cart-item">
                    <div class="item-image">
                        @php
                            $imageUrl = RvMedia::getImageUrl(
                                Arr::get($cartItem->options, 'image', $product->original_product->image ?? ''),
                                'thumb',
                                false,
                                RvMedia::getDefaultImage()
                            );
                        @endphp
                        <img src="{{ $imageUrl }}" alt="{{ $product->original_product->name ?? 'Product' }}">
                    </div>
                    <div class="item-details">
                        <div class="item-name">{{ $product->original_product->name ?? 'Product' }}</div>
                        <div class="item-price">{{ format_price($cartItem->price) }}</div>
                        <div class="item-quantity">(x{{ $cartItem->qty }})</div>
                    </div>
                    <div class="item-remove">
                        <a href="#" class="remove-cart-item" data-url="{{ route('public.cart.remove', $cartItem->rowId) }}">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="3,6 5,6 21,6"></polyline>
                                <path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"></path>
                                <line x1="10" y1="11" x2="10" y2="17"></line>
                                <line x1="14" y1="11" x2="14" y2="17"></line>
                            </svg>
                        </a>
                    </div>
                </div>
            @endif
        @endforeach
    @else
        <div class="empty-cart">
            <p>{{ __('Your cart is empty') }}</p>
        </div>
    @endif
</div>

<!-- Cart Buttons -->
@if (Cart::instance('cart')->isNotEmpty() && Cart::instance('cart')->products()->count())
<div class="modern-cart-buttons">
    <a href="{{ route('public.cart') }}" class="btn-view-cart">{{ __('View Cart') }}</a>
    @php
        $checkoutUrl = '#';
        try {
            if (session('tracked_start_checkout')) {
                $checkoutUrl = route('public.checkout.information', session('tracked_start_checkout'));
            } else {
                $checkoutUrl = route('public.checkout.information');
            }
        } catch (Exception $e) {
            // Fallback to cart page if checkout route doesn't exist
            $checkoutUrl = route('public.cart');
        }
    @endphp
    <a href="{{ $checkoutUrl }}" class="btn-checkout">{{ __('Checkout') }}</a>
</div>
@endif

<style>
/* Modern Cart Styling */

.modern-cart-content {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.modern-cart-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f3f4f6;
}

.modern-cart-item:last-child {
    border-bottom: none;
}

.item-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    background: #f9fafb;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-details {
    flex: 1;
}

.item-name {
    font-size: 14px;
    font-weight: 500;
    color: #111827;
    margin-bottom: 4px;
    line-height: 1.4;
}

.item-price {
    font-size: 16px;
    font-weight: 600;
    color: #f97316;
    margin-bottom: 2px;
}

.item-quantity {
    font-size: 12px;
    color: #6b7280;
}

.item-remove {
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.item-remove:hover {
    background: #fef2f2;
}

.remove-cart-item {
    text-decoration: none;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.remove-cart-item:hover {
    color: #dc2626;
    text-decoration: none;
}

.remove-cart-item svg {
    transition: all 0.2s;
}

.modern-cart-buttons {
    padding: 20px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 12px;
}

.btn-view-cart,
.btn-checkout {
    flex: 1;
    padding: 12px 16px;
    border-radius: 8px;
    text-align: center;
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.2s;
    border: none;
    cursor: pointer;
}

.btn-view-cart {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-view-cart:hover {
    background: #e5e7eb;
    color: #111827;
}

.btn-checkout {
    background: #f97316;
    color: white;
}

.btn-checkout:hover {
    background: #ea580c;
    color: white;
}

.empty-cart {
    text-align: center;
    padding: 40px 20px;
    color: #6b7280;
}
</style>
