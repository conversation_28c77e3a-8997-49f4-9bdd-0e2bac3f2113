$(document).ready(function() {
    let isLoading = false;
    let shownProductIds = [];

    // Collect initially shown product IDs
    $('.more-to-love-products-grid .product-inner').each(function() {
        const productId = $(this).find('.product-thumbnail[data-product-id]').data('product-id');
        if (productId) {
            shownProductIds.push(productId);
        }
    });

    $('.load-more-products').on('click', function() {
        if (isLoading) return;

        const button = $(this);
        const page = parseInt(button.data('page'));
        const loadingText = button.data('loading-text');

        isLoading = true;
        button.addClass('loading');
        button.html(`<span class="button-text">${loadingText.toUpperCase()}</span> <i class="fas fa-spinner button-icon"></i>`);

        $.ajax({
            url: route('public.ajax.more-to-love'),
            method: 'GET',
            data: {
                page: page + 1,
                shown_ids: shownProductIds.join(',')
            },
            success: function(response) {
                if (response.data && response.data.data && response.data.data.products && response.data.data.products.length > 0) {

                    // Debug: Log the request data that was sent
                    console.log('🔍 AJAX REQUEST SENT:');
                    console.log('- Page:', page + 1);
                    console.log('- Shown IDs sent:', shownProductIds.join(','));
                    console.log('- Shown IDs count:', shownProductIds.length);

                    // Check for duplicates BEFORE resetting (this is the real test)
                    if (response.data.data.new_product_ids && !response.data.data.reset_cycle) {
                        const duplicates = response.data.data.new_product_ids.filter(id =>
                            shownProductIds.includes(parseInt(id))
                        );
                        if (duplicates.length > 0) {
                            console.error('🚨 DUPLICATE PRODUCTS DETECTED BEFORE RESET:', duplicates);
                            console.error('🚨 Current shownProductIds:', shownProductIds);
                            console.error('🚨 New product IDs:', response.data.data.new_product_ids);
                        }
                    }

                    const productsHtml = response.data.data.products.join('');
                    $('.more-to-love-products-grid').append(productsHtml);
                    button.data('page', page + 1);

                    // Handle cycle reset - MUST happen before adding new IDs
                    if (response.data.data.reset_cycle) {
                        console.log('🔄 CYCLE RESET DETECTED - Clearing shown products list');
                        shownProductIds = []; // Reset the shown products list
                    }

                    // Add new product IDs to the shown list
                    if (response.data.data.new_product_ids) {
                        shownProductIds = shownProductIds.concat(response.data.data.new_product_ids);
                        console.log('➕ Added new product IDs:', response.data.data.new_product_ids);
                        console.log('📋 Updated shownProductIds:', shownProductIds);
                    }

                    // Initialize lazy loading for new images
                    if (typeof MartApp !== 'undefined') {
                        MartApp.lazyLoad($('.more-to-love-products-grid')[0]);
                    } else if (typeof LazyLoad !== 'undefined') {
                        new LazyLoad({
                            container: $('.more-to-love-products-grid')[0],
                            elements_selector: '.lazyload',
                            callback_error: (img) => {
                                img.setAttribute('src', siteConfig?.img_placeholder || '');
                            }
                        });
                    }

                    // Show cycle reset message if applicable
                    if (response.data.data.reset_cycle && response.data.data.message) {
                        $('.end-of-products').removeClass('d-none');
                        $('.end-of-products').text(response.data.data.message);

                        // Hide message after a brief delay
                        setTimeout(function() {
                            $('.end-of-products').addClass('d-none');
                        }, 3000);
                    }

                    // Debug info (remove in production)
                    console.log('Products loaded:', response.data.data.new_product_ids?.length || 0);
                    console.log('Total products:', response.data.data.total_products);
                    console.log('Shown so far:', response.data.data.shown_count);
                    console.log('Reset cycle:', response.data.data.reset_cycle);
                } else {
                    // No products available at all
                    button.parent().remove();
                    $('.end-of-products').removeClass('d-none');
                    $('.end-of-products').text('No more products available');
                }
            },
            error: function() {
                isLoading = false;
                button.removeClass('loading');
                button.html(`<span class="button-text">VIEW MORE</span> <i class="fas fa-chevron-down button-icon"></i>`);
            },
            complete: function() {
                isLoading = false;
                button.removeClass('loading');
                button.html(`<span class="button-text">VIEW MORE</span> <i class="fas fa-chevron-down button-icon"></i>`);
            }
        });
    });

    // Add extra padding when sticky bar is visible
    function adjustViewMoreButtonPadding() {
        if (window.innerWidth <= 991) {
            const stickyBar = document.querySelector('.mobile-sticky-add-to-cart');
            const viewMoreButton = document.querySelector('.load-more-products');
            const endMessage = document.querySelector('.end-of-products');

            if (stickyBar) {
                const stickyBarHeight = stickyBar.offsetHeight;

                if (viewMoreButton) {
                    viewMoreButton.closest('div').style.marginBottom = (stickyBarHeight + 20) + 'px';
                    viewMoreButton.closest('div').style.paddingBottom = '20px';
                }

                if (endMessage && !endMessage.classList.contains('d-none')) {
                    endMessage.style.marginBottom = (stickyBarHeight + 20) + 'px';
                    endMessage.style.paddingBottom = '20px';
                }
            }
        }
    }

    // Run on page load
    adjustViewMoreButtonPadding();

    // Run on window resize
    window.addEventListener('resize', adjustViewMoreButtonPadding);
});
