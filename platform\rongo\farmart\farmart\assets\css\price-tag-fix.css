/* Fix to make green price tags the same size as orange ones */

/* Target all price tags to ensure consistent styling */
.product-price ins,
.product-price .amount,
.product-price-original bdi,
.product-price-sale bdi,
.price-amount,
.price-amount .amount,
.product-price-original .price-amount,
.product-price-sale .price-amount,
.product-inner .product-price,
.product-inner .product-price-original,
.product-inner .product-price-sale,
.product-inner .product-price span,
.product-inner .product-price-original span,
.product-inner .product-price-sale span {
    font-size: 18px !important;
    font-weight: 700 !important;
    line-height: 1.5 !important;
}

/* Specifically target the green price tags */
.product-inner .product-price[style*="color: green"],
.product-inner .product-price[style*="color:green"],
.product-inner .product-price .amount[style*="color: green"],
.product-inner .product-price .amount[style*="color:green"],
.product-inner .product-price-original[style*="color: green"],
.product-inner .product-price-original[style*="color:green"],
.product-inner .product-price-sale[style*="color: green"],
.product-inner .product-price-sale[style*="color:green"],
.product-inner .price-amount[style*="color: green"],
.product-inner .price-amount[style*="color:green"] {
    font-size: 18px !important;
    font-weight: 700 !important;
    color: #ff6633 !important; /* Match the orange color */
}

/* Target the specific green price tags */
.product-price .text-success,
.product-price-original .text-success,
.product-price-sale .text-success,
.price-amount .text-success,
.product-inner span[style*="color: green"],
.product-inner span[style*="color:green"],
.product-inner .text-success {
    font-size: 18px !important;
    font-weight: 700 !important;
    color: #ff6633 !important; /* Match the orange color */
}

/* Fix for the specific green price tags shown in the image */
span[style*="color: green"],
span[style*="color:green"],
.text-success {
    font-size: 18px !important;
    font-weight: 700 !important;
    color: #ff6633 !important; /* Match the orange color */
}

/* Ensure consistent styling for all price amounts */
.amount:not(del .amount):not(del bdi):not(del span),
.product-inner .amount:not(del .amount):not(del bdi):not(del span),
.product-inner .product-price .amount:not(del .amount):not(del bdi):not(del span),
.product-inner .product-price-original .amount:not(del .amount):not(del bdi):not(del span),
.product-inner .product-price-sale .amount:not(del .amount):not(del bdi):not(del span),
.product-inner .price-amount .amount:not(del .amount):not(del bdi):not(del span),
.product-price,
.product-price-original,
.product-price-sale,
.product-price ins,
.product-price-original .price-amount,
.product-price-sale ins,
.product-price-sale ins .price-amount,
.product-price-sale ins .price-amount .amount {
    font-size: 18px !important;
    font-weight: 700 !important;
    color: #ff6633 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Product Detail Page - Large Price Display (Desktop Only) */
@media (min-width: 993px) {
    .product-detail-container .product-details .product-price .amount:not(del .amount):not(del bdi):not(del span),
    .product-detail-container .product-details .product-price-original .amount:not(del .amount):not(del bdi):not(del span),
    .product-detail-container .product-details .product-price-sale .amount:not(del .amount):not(del bdi):not(del span),
    .product-detail-container .product-details .product-price ins .amount,
    .product-detail-container .product-details .product-price-sale ins .amount,
    .product-detail-container .product-details .price-amount .amount:not(del .amount):not(del bdi):not(del span),
    body.single-product .product-details .product-price .amount:not(del .amount),
    body.single-product .product-details .product-price-original .amount:not(del .amount),
    body.single-product .product-details .product-price-sale ins .amount {
        font-size: 48px !important;
        font-weight: 700 !important;
        color: #ff6633 !important;
        display: inline !important;
        visibility: visible !important;
        opacity: 1 !important;
        line-height: 1.2 !important;
    }
}

/* Product Detail Page - Mobile Price Display */
@media (max-width: 992px) {
    .product-detail-container .product-details .product-price .amount:not(del .amount):not(del bdi):not(del span),
    .product-detail-container .product-details .product-price-original .amount:not(del .amount):not(del bdi):not(del span),
    .product-detail-container .product-details .product-price-sale .amount:not(del .amount):not(del bdi):not(del span),
    .product-detail-container .product-details .product-price ins .amount,
    .product-detail-container .product-details .product-price-sale ins .amount,
    .product-detail-container .product-details .price-amount .amount:not(del .amount):not(del bdi):not(del span) {
        font-size: 28px !important;
        font-weight: 700 !important;
        color: #ff6633 !important;
        display: inline !important;
        visibility: visible !important;
        opacity: 1 !important;
        line-height: 1.2 !important;
    }
}

/* Product Detail Page - Strikethrough Original Price */
.product-detail-container .product-details .product-price del .amount,
.product-detail-container .product-details .product-price-sale del .amount,
.product-detail-container .product-details del .amount {
    font-size: 20px !important;
    color: #999 !important;
    text-decoration: line-through !important;
    opacity: 0.8 !important;
    font-weight: normal !important;
    margin-left: 8px !important;
}

/* Desktop Price Tag - Make main price bigger on desktop (for product cards only) */
@media (min-width: 993px) {
    /* Main product card price (not detail page) */
    .product-inner .amount:not(del .amount):not(del bdi):not(del span),
    .product-inner .product-price .amount:not(del .amount):not(del bdi):not(del span),
    .product-inner .product-price-original .amount:not(del .amount):not(del bdi):not(del span),
    .product-inner .product-price-sale .amount:not(del .amount):not(del bdi):not(del span),
    .product-inner .price-amount .amount:not(del .amount):not(del bdi):not(del span) {
        font-size: 28px !important;
        font-weight: 700 !important;
        color: #ff6633 !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Keep original price smaller on desktop for product cards */
    .product-inner .product-price del .amount,
    .product-inner .product-price-sale del .amount,
    .product-inner del .amount {
        font-size: 18px !important;
        text-decoration: line-through !important;
        opacity: 0.8 !important;
    }
}

/* Fix for deleted prices */
.product-price del,
.product-price-sale del,
del,
.product-inner del,
.product-inner .product-price del,
.product-inner .product-price-sale del,
.product-inner .price-amount del {
    color: #999 !important;
    font-weight: normal !important;
    font-size: 14px !important;
    margin-left: 5px !important;
    text-decoration: line-through !important;
}

/* Specifically target the crossed-out original price */
del .amount,
del bdi,
del span,
.product-inner del .amount,
.product-inner del bdi,
.product-inner del span,
.product-price-sale del .amount,
.product-price-sale del bdi,
.product-price-sale del span,
.product-inner .product-price-sale del .amount,
.product-inner .product-price-sale del bdi,
.product-inner .product-price-sale del span,
.product-inner small del,
.product-inner small del .amount,
.product-inner small del bdi,
.product-inner small del span {
    color: #999 !important;
    font-weight: normal !important;
    font-size: 14px !important;
    text-decoration: line-through !important;
}

/* Force price visibility - override any hiding */
.product-price,
.product-price-original,
.product-price-sale,
.product-inner .product-price,
.product-inner .product-price-original,
.product-inner .product-price-sale {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: static !important;
    left: auto !important;
    color: #ff6633 !important;
    font-weight: 700 !important;
}

/* Ensure price amounts are visible */
.product-price .amount,
.product-price-original .amount,
.product-price-sale .amount,
.product-price ins .amount,
.product-price-sale ins .amount {
    display: inline !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: #ff6633 !important;
    font-weight: 700 !important;
}

/* Additional Product Detail Page Price Styling */
.product-detail-container .product-details-content .product-price,
.product-detail-container .product-details-content .product-price-original,
.product-detail-container .product-details-content .product-price-sale {
    margin-bottom: 15px !important;
    line-height: 1.3 !important;
}

/* Ensure product detail page prices are larger than product cards - Desktop Only */
@media (min-width: 993px) {
    body.single-product .product-details .product-price .amount:not(del .amount),
    body.single-product .product-details .product-price-original .amount:not(del .amount),
    body.single-product .product-details .product-price-sale ins .amount {
        font-size: 48px !important;
        font-weight: 700 !important;
        color: #ff6633 !important;
    }
}

/* Mobile responsive for product detail page */
@media (max-width: 992px) {
    body.single-product .product-details .product-price .amount:not(del .amount),
    body.single-product .product-details .product-price-original .amount:not(del .amount),
    body.single-product .product-details .product-price-sale ins .amount {
        font-size: 28px !important;
        font-weight: 700 !important;
        color: #ff6633 !important;
    }
}

/* SUPER AGGRESSIVE DESKTOP PRODUCT DETAIL PAGE PRICE STYLING */
@media (min-width: 993px) {
    /* Target ALL possible price elements on product detail pages with maximum specificity */
    html body.single-product .product-detail-container .product-details-content .product-price .amount,
    html body.single-product .product-detail-container .product-details-content .product-price-original .amount,
    html body.single-product .product-detail-container .product-details-content .product-price-sale ins .amount,
    html body.single-product .product-detail-container .product-details-content .product-price ins .amount,
    html body.single-product .product-detail-container .product-details-content .price-amount .amount,
    html body.single-product .product-detail-container .col-lg-4 .product-details .product-price .amount,
    html body.single-product .product-detail-container .col-lg-4 .product-details .product-price-original .amount,
    html body.single-product .product-detail-container .col-lg-4 .product-details .product-price-sale ins .amount,
    html body.single-product .product-detail-container .product-price .amount:not(del .amount),
    html body.single-product .product-detail-container .product-price-original .amount:not(del .amount),
    html body.single-product .product-detail-container .product-price-sale ins .amount:not(del .amount),
    html body.single-product .product-detail-container span.amount:not(del span.amount),
    html body.single-product .product-detail-container .product-price span.amount:not(del span.amount),
    html body.single-product .product-detail-container .product-price-sale ins span.amount,
    html body.single-product .product-detail-container .product-price-original span.amount:not(del span.amount) {
        font-size: 42px !important;
        font-weight: 700 !important;
        color: #ff6633 !important;
        display: inline !important;
        visibility: visible !important;
        opacity: 1 !important;
        line-height: 1.1 !important;
        text-transform: none !important;
        letter-spacing: normal !important;
    }

    /* Only show the first strikethrough price */
    html body.single-product .product-detail-container .product-details .product-price del:first-of-type .amount,
    html body.single-product .product-detail-container .product-details .product-price-sale del:first-of-type .amount,
    html body.single-product .product-detail-container del:first-of-type .amount,
    html body.single-product .product-detail-container del:first-of-type span.amount {
        font-size: 22px !important;
        color: #999 !important;
        text-decoration: line-through !important;
        opacity: 0.8 !important;
        font-weight: normal !important;
        margin-left: 10px !important;
    }

    /* Hide any additional strikethrough prices */
    html body.single-product .product-detail-container .product-price del:not(:first-of-type),
    html body.single-product .product-detail-container del:not(:first-of-type) {
        display: none !important;
    }
}
